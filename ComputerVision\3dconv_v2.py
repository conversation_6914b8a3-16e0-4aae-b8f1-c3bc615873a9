import cv2
import numpy as np
from matplotlib import pyplot as plt
from ultralytics import YOLO
from pathlib import Path
from typing import Tuple, List, Optional
from scipy.spatial.transform import Rotation
from dataclasses import dataclass
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CameraParameters:
    """Camera intrinsic parameters and distortion coefficients."""
    K: np.ndarray  # Intrinsic matrix
    dist_coeffs: Optional[np.ndarray] = None  # Distortion coefficients
    
    def __post_init__(self):
        if self.dist_coeffs is None:
            self.dist_coeffs = np.zeros(5)

@dataclass
class StereoSetup:
    """Stereo camera setup configuration."""
    camera1: CameraParameters
    camera2: CameraParameters
    R: Optional[np.ndarray] = None  # Rotation matrix
    t: Optional[np.ndarray] = None  # Translation vector

class PoseKeypoints:
    """Defines human pose keypoint connections and indices."""
    
    # COCO pose keypoint indices
    NOSE = 0
    LEFT_EYE = 1
    RIGHT_EYE = 2
    LEFT_EAR = 3
    RIGHT_EAR = 4
    LEFT_SHOULDER = 5
    RIGHT_SHOULDER = 6
    LEFT_ELBOW = 7
    RIGHT_ELBOW = 8
    LEFT_WRIST = 9
    RIGHT_WRIST = 10
    LEFT_HIP = 11
    RIGHT_HIP = 12
    LEFT_KNEE = 13
    RIGHT_KNEE = 14
    LEFT_ANKLE = 15
    RIGHT_ANKLE = 16
    
    # Skeleton connections for visualization
    CONNECTIONS = [
        (NOSE, LEFT_EYE), (NOSE, RIGHT_EYE),
        (LEFT_EYE, LEFT_EAR), (RIGHT_EYE, RIGHT_EAR),
        (LEFT_SHOULDER, RIGHT_SHOULDER),
        (LEFT_SHOULDER, LEFT_ELBOW), (LEFT_ELBOW, LEFT_WRIST),
        (RIGHT_SHOULDER, RIGHT_ELBOW), (RIGHT_ELBOW, RIGHT_WRIST),
        (LEFT_SHOULDER, LEFT_HIP), (RIGHT_SHOULDER, RIGHT_HIP),
        (LEFT_HIP, RIGHT_HIP),
        (LEFT_HIP, LEFT_KNEE), (LEFT_KNEE, LEFT_ANKLE),
        (RIGHT_HIP, RIGHT_KNEE), (RIGHT_KNEE, RIGHT_ANKLE)
    ]

class HumanPoseEstimator3D:
    """3D Human Pose Estimation using stereo vision."""
    
    def __init__(self, model_path: str, confidence_threshold: float = 0.5):
        """
        Initialize the 3D pose estimator.
        
        Args:
            model_path: Path to YOLO pose model
            confidence_threshold: Minimum confidence for keypoint detection
        """
        self.model = YOLO(model_path)
        self.confidence_threshold = confidence_threshold
        self.stereo_setup: Optional[StereoSetup] = None
        
    def detect_keypoints(self, image_path: str) -> List[np.ndarray]:
        """
        Detect 2D keypoints in an image.
        
        Args:
            image_path: Path to input image
            
        Returns:
            List of keypoint arrays for each detected person
        """
        results = self.model(image_path)[0]
        keypoints_list = []
        
        if results.keypoints is not None:
            keypoints_data = results.keypoints.data.numpy()
            for person_keypoints in keypoints_data:
                # Filter keypoints by confidence
                valid_keypoints = person_keypoints[person_keypoints[:, 2] > self.confidence_threshold]
                if len(valid_keypoints) >= 5:  # Minimum keypoints required
                    keypoints_list.append(person_keypoints)
                    
        return keypoints_list
    
    def match_keypoints_stereo(self, img1_path: str, img2_path: str) -> Tuple[np.ndarray, np.ndarray]:
        """
        Find matching keypoints between stereo image pair.
        
        Args:
            img1_path: Path to left camera image
            img2_path: Path to right camera image
            
        Returns:
            Tuple of matched keypoint arrays (points1, points2)
        """
        keypoints1_list = self.detect_keypoints(img1_path)
        keypoints2_list = self.detect_keypoints(img2_path)
        
        matched_points1, matched_points2 = [], []
        
        # Match keypoints between the closest persons in both images
        for kpts1 in keypoints1_list:
            for kpts2 in keypoints2_list:
                valid_indices = []
                for i in range(len(kpts1)):
                    if (kpts1[i][2] > self.confidence_threshold and 
                        kpts2[i][2] > self.confidence_threshold):
                        valid_indices.append(i)
                
                if len(valid_indices) >= 5:
                    matched_points1.extend([kpts1[i][:2] for i in valid_indices])
                    matched_points2.extend([kpts2[i][:2] for i in valid_indices])
                    break  # Use first valid match
            
            if matched_points1:  # Break if we found matches
                break
        
        return (np.float32(matched_points1).reshape(-1, 2), 
                np.float32(matched_points2).reshape(-1, 2))
    
    def process_multiple_frames(self, frame_pairs: List[Tuple[str, str]]) -> Tuple[np.ndarray, np.ndarray]:
        """
        Process multiple stereo frame pairs to get robust keypoint matches.
        
        Args:
            frame_pairs: List of (left_image_path, right_image_path) tuples
            
        Returns:
            Combined matched keypoints from all frames
        """
        all_points1, all_points2 = [], []
        
        for img1_path, img2_path in frame_pairs:
            try:
                points1, points2 = self.match_keypoints_stereo(img1_path, img2_path)
                logger.info(f"Matched {len(points1)} keypoints in frame pair")
                if len(points1) > 0 and len(points2) > 0:
                    all_points1.append(points1)
                    all_points2.append(points2)
                    logger.info(f"Processed frame pair: {len(points1)} keypoints")
            except Exception as e:
                logger.warning(f"Failed to process frame pair {img1_path}, {img2_path}: {e}")
        
        if not all_points1:
            raise ValueError("No valid keypoints found in any frame pair")
        
        return np.vstack(all_points1), np.vstack(all_points2)
    
    def estimate_fundamental_matrix(self, points1: np.ndarray, points2: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Estimate fundamental matrix using RANSAC.
        
        Args:
            points1: Keypoints from left camera
            points2: Keypoints from right camera
            
        Returns:
            Tuple of (fundamental_matrix, filtered_points1, filtered_points2)
        """
        F, mask = cv2.findFundamentalMat(points1, points2, cv2.FM_RANSAC, 0.1799, 0.9999)
        
        if F is None:
            raise ValueError("Failed to estimate fundamental matrix")
        
        good_points1 = points1[mask.ravel() == 1]
        good_points2 = points2[mask.ravel() == 1]
        
        logger.info(f"RANSAC filtered points: {len(good_points1)}/{len(points1)}")
        
        return F, good_points1, good_points2
    
    def calibrate_stereo(self, points1: np.ndarray, points2: np.ndarray, 
                        camera1: CameraParameters, camera2: CameraParameters) -> StereoSetup:
        """
        Calibrate stereo setup from corresponding points.
        
        Args:
            points1: Keypoints from camera 1
            points2: Keypoints from camera 2
            camera1: Camera 1 parameters
            camera2: Camera 2 parameters
            
        Returns:
            Calibrated stereo setup
        """
        # Estimate fundamental matrix
        F, good_points1, good_points2 = self.estimate_fundamental_matrix(points1, points2)
        
        # Compute essential matrix
        E = camera2.K.T @ F @ camera1.K
        
        # Normalize points
        points1_norm = cv2.undistortPoints(
            good_points1.reshape(-1, 1, 2), camera1.K, None
        ).reshape(-1, 2)
        
        points2_norm = cv2.undistortPoints(
            good_points2.reshape(-1, 1, 2), camera2.K, None
        ).reshape(-1, 2)
        
        # Recover pose
        points_count, R, t, mask_pose = cv2.recoverPose(E, points1_norm, points2_norm)
        logger.info(f"Pose recovery used {points_count} points")
        
        stereo_setup = StereoSetup(camera1, camera2, R, t)
        self.stereo_setup = stereo_setup
        
        return stereo_setup
    
    def triangulate_points(self, points1: np.ndarray, points2: np.ndarray) -> np.ndarray:
        """
        Triangulate 3D points from stereo correspondences.
        
        Args:
            points1: 2D points from camera 1
            points2: 2D points from camera 2
            
        Returns:
            3D points in world coordinates
        """
        if self.stereo_setup is None:
            raise ValueError("Stereo setup not calibrated. Call calibrate_stereo first.")
        
        # Create projection matrices
        proj1 = self.stereo_setup.camera1.K @ np.hstack([np.eye(3), np.zeros((3, 1))])
        proj2 = self.stereo_setup.camera2.K @ np.hstack([self.stereo_setup.R, self.stereo_setup.t])
        
        # Triangulate points
        points_4d = cv2.triangulatePoints(proj1, proj2, points1.T, points2.T)
        points_3d = points_4d[:3] / points_4d[3]
        points_3d = points_3d.T
        
        # Filter points with reasonable depth
        valid_depth = (points_3d[:, 2] > 0) & (points_3d[:, 2] < 1000)
        points_3d_filtered = points_3d[valid_depth]
        
        logger.info(f"Triangulated {len(points_3d_filtered)}/{len(points_3d)} valid points")
        
        return points_3d_filtered
    
    def scale_and_orient_pose(self, points_3d: np.ndarray, 
                            real_distance: float = 1) -> np.ndarray:
        """
        Scale and orient 3D pose to realistic proportions.
        
        Args:
            points_3d: Raw 3D keypoints
            real_distance: Real-world distance between hips in meters
            
        Returns:
            Scaled and oriented 3D points
        """
        if len(points_3d) < 14:  # Need at least hip and ankle keypoints
            logger.warning("Insufficient keypoints for pose orientation")
            return points_3d
        
        # Scale based on hip distance
        hip_left = points_3d[PoseKeypoints.LEFT_HIP]
        hip_right = points_3d[PoseKeypoints.RIGHT_HIP]
        current_distance = np.linalg.norm(hip_left - hip_right)
        
        if current_distance > 0:
            scale_factor = real_distance / current_distance
            points_3d_scaled = points_3d * scale_factor
        else:
            points_3d_scaled = points_3d.copy()
        
        # Orient pose vertically
        try:
            hip_center = (points_3d_scaled[PoseKeypoints.LEFT_HIP] + 
                         points_3d_scaled[PoseKeypoints.RIGHT_HIP]) / 2
            ankle_center = (points_3d_scaled[PoseKeypoints.LEFT_ANKLE] + 
                           points_3d_scaled[PoseKeypoints.RIGHT_ANKLE]) / 2
            
            up_vector = hip_center - ankle_center
            up_vector = up_vector / np.linalg.norm(up_vector)
            
            # Rotate to align with Z-axis
            target = np.array([0, 0, 1])
            rotation_axis = np.cross(up_vector, target)
            angle = np.arccos(np.clip(np.dot(up_vector, target), -1, 1))
            
            if np.linalg.norm(rotation_axis) > 1e-6:
                rotation = Rotation.from_rotvec(rotation_axis * angle)
                points_3d_oriented = rotation.apply(points_3d_scaled)
                
                # Center at hip
                hip_center_rotated = rotation.apply(hip_center.reshape(1, -1))[0]
                points_3d_oriented -= hip_center_rotated
                
                return points_3d_oriented
        except (IndexError, ValueError) as e:
            logger.warning(f"Could not orient pose: {e}")
        
        return points_3d_scaled

class PoseVisualizer:
    """3D pose visualization utilities."""
    
    @staticmethod
    def plot_3d_pose(points_3d: np.ndarray, title: str = "3D Human Pose", 
                     figsize: Tuple[int, int] = (12, 9)) -> None:
        """
        Visualize 3D human pose with skeleton connections.
        
        Args:
            points_3d: 3D keypoints array
            title: Plot title
            figsize: Figure size tuple
        """
        fig = plt.figure(figsize=figsize)
        ax = fig.add_subplot(111, projection='3d')
        
        # Plot keypoints
        ax.scatter(points_3d[:, 0], points_3d[:, 1], points_3d[:, 2], 
                  c='red', s=100, alpha=0.8, label='Keypoints')
        
        # Draw skeleton connections
        for connection in PoseKeypoints.CONNECTIONS:
            if (connection[0] < len(points_3d) and 
                connection[1] < len(points_3d)):
                point1 = points_3d[connection[0]]
                point2 = points_3d[connection[1]]
                ax.plot([point1[0], point2[0]], 
                       [point1[1], point2[1]], 
                       [point1[2], point2[2]], 
                       'b-', linewidth=2, alpha=0.7)
        
        # Set labels and limits
        ax.set_xlabel('X (m)', fontsize=12)
        ax.set_ylabel('Y (m)', fontsize=12)
        ax.set_zlabel('Z (m)', fontsize=12)
        ax.set_title(title, fontsize=14)
        
        # Equal aspect ratio
        max_range = np.array([points_3d[:, 0].max() - points_3d[:, 0].min(),
                             points_3d[:, 1].max() - points_3d[:, 1].min(),
                             points_3d[:, 2].max() - points_3d[:, 2].min()]).max() / 2.0
        
        mid_x = (points_3d[:, 0].max() + points_3d[:, 0].min()) * 0.5
        mid_y = (points_3d[:, 1].max() + points_3d[:, 1].min()) * 0.5
        mid_z = (points_3d[:, 2].max() + points_3d[:, 2].min()) * 0.5
        
        ax.set_xlim(mid_x - max_range, mid_x + max_range)
        ax.set_ylim(mid_y - max_range, mid_y + max_range)
        ax.set_zlim(mid_z - max_range, mid_z + max_range)
        
        ax.legend()
        plt.tight_layout()
        plt.show()

def main():
    """Main function demonstrating the refactored 3D pose estimation."""
    
    # Configuration
    model_path = "yolov8n-pose.pt"  # Update with actual path
    
    # Camera parameters
    camera1 = CameraParameters(
        K=np.array([[955.64, 0, 784.75], 
                   [0, 902.67, 1040.39], 
                   [0, 0, 1]])
    )
    
    camera2 = CameraParameters(
        K=np.array([[1328.61, 0, 507.68], 
                   [0, 1331.32, 951.98], 
                   [0, 0, 1]])
    )
    
    # Frame pairs (update with actual paths)
    frame_pairs = [
        ("D:\\AI\\CameraCalib\\human_frame_and\\frame_001667.jpg", "D:\\AI\\CameraCalib\\human_frame_ip\\frame_001199.jpg"),
        ("D:\\AI\\CameraCalib\\human_frame_and\\frame_001672.jpg", "D:\\AI\\CameraCalib\\human_frame_ip\\frame_001204.jpg"),
        ("D:\\AI\\CameraCalib\\human_frame_and\\frame_001677.jpg", "D:\\AI\\CameraCalib\\human_frame_ip\\frame_001209.jpg"),
        ("D:\\AI\\CameraCalib\\human_frame_and\\frame_001682.jpg", "D:\\AI\\CameraCalib\\human_frame_ip\\frame_001214.jpg"),
        ("D:\\AI\\CameraCalib\\human_frame_and\\frame_001687.jpg", "D:\\AI\\CameraCalib\\human_frame_ip\\frame_001219.jpg")
    ]
    
    frame_pairs_tan = [
        ("D:\\AI\\CameraCalib\\human_frame_and\\frame_001628.jpg", "D:\\AI\\CameraCalib\\human_frame_ip\\frame_001160.jpg")    
    ]


    try:
        # Initialize estimator
        estimator = HumanPoseEstimator3D(model_path, confidence_threshold=0.5)
        
        # Process multiple frames
        points1, points2 = estimator.process_multiple_frames(frame_pairs)
        logger.info(f"Total matched points: {len(points1)}")
        
        # Calibrate stereo setup
        stereo_setup = estimator.calibrate_stereo(points1, points2, camera1, camera2)
        
        # Triangulate 3D points for first frame pair
        points1_single, points2_single = estimator.match_keypoints_stereo(
            frame_pairs[1][0], frame_pairs[1][1]
        )

        if len(points1_single) > 0:
            points_3d = estimator.triangulate_points(points1_single, points2_single)
            
            # Scale and orient pose
            points_3d_final = estimator.scale_and_orient_pose(points_3d)
            
            # Visualize results
            print(f"Final 3D points shape: {points_3d_final.shape}")
            print("3D coordinates:")
            print(points_3d_final)
            
            PoseVisualizer.plot_3d_pose(points_3d_final, "Reconstructed 3D Human Pose")
        else:
            logger.error("No keypoints detected in the first frame pair")
            
    except Exception as e:
        logger.error(f"3D pose estimation failed: {e}")
        raise

if __name__ == "__main__":
    main()